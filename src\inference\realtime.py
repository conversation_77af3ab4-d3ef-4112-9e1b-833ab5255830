import cv2
import numpy as np
import time
import logging
from datetime import datetime
from typing import Dict, List, Optional, Callable
from ..models.threat_detector import CCTVThreatDetector
import threading
import queue

class RealtimeProcessor:
    """Real-time threat detection processor for various input sources"""
    
    def __init__(self, model_path: str, confidence_threshold: float = 0.7):
        self.detector = CCTVThreatDetector.from_pretrained(model_path, 
                                                          confidence_threshold=confidence_threshold)
        self.logger = logging.getLogger(__name__)
        self.is_processing = False
        self.frame_queue = queue.Queue(maxsize=10)
        self.result_queue = queue.Queue()
        
    def process_webcam(self, camera_id: int = 0, 
                      alert_callback: Callable = None,
                      display: bool = True) -> None:
        """Process webcam feed for threat detection"""
        cap = cv2.VideoCapture(camera_id)
        
        if not cap.isOpened():
            self.logger.error(f"Failed to open camera {camera_id}")
            return
        
        self.logger.info(f"Started webcam processing on camera {camera_id}")
        self.is_processing = True
        frame_count = 0
        threat_count = 0
        
        try:
            while self.is_processing:
                ret, frame = cap.read()
                if not ret:
                    self.logger.warning("Failed to read frame from webcam")
                    break
                
                frame_count += 1
                
                # Process every 3rd frame for performance
                if frame_count % 3 == 0:
                    result = self.detector.predict_image(frame)
                    
                    if result['is_threat'] and result['confidence'] > self.detector.confidence_threshold:
                        threat_count += 1
                        self.logger.warning(f"THREAT #{threat_count}: {result['confidence']:.2%} confidence")
                        
                        if alert_callback:
                            alert_callback(frame, result, {
                                'timestamp': datetime.now(),
                                'frame_number': frame_count,
                                'threat_number': threat_count,
                                'source': f'webcam_{camera_id}'
                            })
                    
                    if display:
                        self._display_frame(frame, result, frame_count, threat_count)
                        
                        if cv2.waitKey(1) & 0xFF == ord('q'):
                            break
                
        except KeyboardInterrupt:
            self.logger.info("Webcam processing stopped by user")
        
        finally:
            self.is_processing = False
            cap.release()
            if display:
                cv2.destroyAllWindows()
    
    def process_video_file(self, video_path: str,
                          output_path: str = None,
                          alert_callback: Callable = None,
                          frame_skip: int = 1) -> List[Dict]:
        """Process video file and return threat detection results"""
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            self.logger.error(f"Failed to open video file: {video_path}")
            return []
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        self.logger.info(f"Processing video: {video_path}")
        self.logger.info(f"Total frames: {total_frames}, FPS: {fps}")
        
        results = []
        frame_count = 0
        threat_count = 0
        
        # Setup video writer if output path provided
        writer = None
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            frame_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            frame_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            writer = cv2.VideoWriter(output_path, fourcc, fps, (frame_width, frame_height))
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                frame_count += 1
                
                # Skip frames if specified
                if frame_count % frame_skip != 0:
                    continue
                
                # Detect threats
                result = self.detector.predict_image(frame)
                result['frame_number'] = frame_count
                result['timestamp'] = frame_count / fps
                
                if result['is_threat']:
                    threat_count += 1
                    self.logger.info(f"Threat detected at frame {frame_count} ({result['timestamp']:.1f}s)")
                    
                    if alert_callback:
                        alert_callback(frame, result, {
                            'timestamp': datetime.now(),
                            'frame_number': frame_count,
                            'threat_number': threat_count,
                            'source': video_path
                        })
                
                results.append(result)
                
                # Write frame with overlay if output specified
                if writer:
                    overlay_frame = self._create_overlay_frame(frame, result)
                    writer.write(overlay_frame)
                
                # Progress logging
                if frame_count % 100 == 0:
                    progress = (frame_count / total_frames) * 100
                    self.logger.info(f"Progress: {progress:.1f}% ({frame_count}/{total_frames})")
        
        finally:
            cap.release()
            if writer:
                writer.release()
        
        self.logger.info(f"Video processing complete. Total threats detected: {threat_count}")
        return results
    
    def process_image_batch(self, image_paths: List[str],
                           alert_callback: Callable = None) -> List[Dict]:
        """Process batch of images"""
        results = []
        threat_count = 0
        
        self.logger.info(f"Processing batch of {len(image_paths)} images")
        
        for i, image_path in enumerate(image_paths):
            try:
                result = self.detector.predict_image(image_path)
                result['image_path'] = image_path
                result['batch_index'] = i
                
                if result['is_threat']:
                    threat_count += 1
                    self.logger.info(f"Threat detected in {image_path}")
                    
                    if alert_callback:
                        # Load image for callback
                        image = cv2.imread(image_path)
                        alert_callback(image, result, {
                            'timestamp': datetime.now(),
                            'image_path': image_path,
                            'threat_number': threat_count,
                            'source': 'batch_processing'
                        })
                
                results.append(result)
                
            except Exception as e:
                self.logger.error(f"Error processing {image_path}: {e}")
                results.append({
                    'image_path': image_path,
                    'batch_index': i,
                    'error': str(e),
                    'is_threat': False,
                    'confidence': 0.0
                })
        
        self.logger.info(f"Batch processing complete. Total threats detected: {threat_count}")
        return results
    
    def start_threaded_processing(self, input_source,
                                 alert_callback: Callable = None) -> None:
        """Start threaded processing for real-time performance"""
        self.is_processing = True
        
        # Start frame capture thread
        capture_thread = threading.Thread(
            target=self._capture_frames,
            args=(input_source,)
        )
        
        # Start processing thread
        process_thread = threading.Thread(
            target=self._process_frames,
            args=(alert_callback,)
        )
        
        capture_thread.start()
        process_thread.start()
        
        return capture_thread, process_thread
    
    def stop_processing(self):
        """Stop all processing threads"""
        self.is_processing = False
    
    def _capture_frames(self, input_source):
        """Capture frames in separate thread"""
        if isinstance(input_source, int):
            cap = cv2.VideoCapture(input_source)  # Webcam
        else:
            cap = cv2.VideoCapture(input_source)  # Video file or stream
        
        frame_count = 0
        
        while self.is_processing and cap.isOpened():
            ret, frame = cap.read()
            if not ret:
                break
            
            frame_count += 1
            
            # Add frame to queue (skip if queue is full)
            try:
                self.frame_queue.put((frame, frame_count), timeout=0.01)
            except queue.Full:
                pass  # Skip frame if queue is full
        
        cap.release()
    
    def _process_frames(self, alert_callback: Callable = None):
        """Process frames in separate thread"""
        threat_count = 0
        
        while self.is_processing:
            try:
                frame, frame_count = self.frame_queue.get(timeout=1.0)
                
                # Detect threats
                result = self.detector.predict_image(frame)
                result['frame_number'] = frame_count
                
                if result['is_threat']:
                    threat_count += 1
                    
                    if alert_callback:
                        alert_callback(frame, result, {
                            'timestamp': datetime.now(),
                            'frame_number': frame_count,
                            'threat_number': threat_count,
                            'source': 'threaded_processing'
                        })
                
                # Add result to result queue
                self.result_queue.put((frame, result))
                
            except queue.Empty:
                continue
    
    def _display_frame(self, frame, result, frame_count, threat_count):
        """Display frame with overlay information"""
        overlay_frame = self._create_overlay_frame(frame, result)
        
        # Add frame and threat counters
        cv2.putText(overlay_frame, f"Frame: {frame_count}", (10, 70), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(overlay_frame, f"Threats: {threat_count}", (10, 100), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        cv2.imshow('CCTV Threat Detection - Realtime', overlay_frame)
    
    def _create_overlay_frame(self, frame, result):
        """Create frame with prediction overlay"""
        overlay_frame = frame.copy()
        
        if result['is_threat']:
            color = (0, 0, 255)  # Red
            text = f"THREAT: {result['confidence']:.2%}"
        else:
            color = (0, 255, 0)  # Green
            text = f"NORMAL: {result['confidence']:.2%}"
        
        # Add text overlay
        cv2.putText(overlay_frame, text, (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, color, 2)
        
        # Add confidence bar
        bar_width = int(200 * result['confidence'])
        cv2.rectangle(overlay_frame, (10, 50), (10 + bar_width, 70), color, -1)
        cv2.rectangle(overlay_frame, (10, 50), (210, 70), (255, 255, 255), 2)
        
        return overlay_frame

def main():
    """Main function for command line usage"""
    import argparse
    
    parser = argparse.ArgumentParser(description='CCTV Threat Detection - Real-time Processing')
    parser.add_argument('--model', required=True, help='Path to model or Hugging Face model name')
    parser.add_argument('--source', default=0, help='Input source (webcam ID, video file, or stream URL)')
    parser.add_argument('--confidence', type=float, default=0.7, help='Confidence threshold')
    parser.add_argument('--output', help='Output video path (optional)')
    
    args = parser.parse_args()
    
    # Initialize processor
    processor = RealtimeProcessor(args.model, confidence_threshold=args.confidence)
    
    # Determine source type
    try:
        source = int(args.source)  # Webcam
        processor.process_webcam(source)
    except ValueError:
        # Video file or stream
        if args.output:
            processor.process_video_file(args.source, args.output)
        else:
            processor.process_video_file(args.source)

if __name__ == "__main__":
    main()
