import cv2
import time
import logging
from datetime import datetime
from typing import Dict, Optional, Callable
from ..models.threat_detector import CCTVThreatDetector

class IPCameraProcessor:
    """Process IP camera streams for threat detection"""
    
    def __init__(self, model_path: str, confidence_threshold: float = 0.7):
        self.detector = CCTVThreatDetector.from_pretrained(model_path, 
                                                          confidence_threshold=confidence_threshold)
        self.logger = logging.getLogger(__name__)
        
    def connect_camera(self, camera_url: str, username: str = None, password: str = None) -> Optional[cv2.VideoCapture]:
        """Connect to IP camera"""
        if username and password:
            if 'rtsp://' in camera_url:
                camera_url = camera_url.replace('rtsp://', f'rtsp://{username}:{password}@')
            elif 'http://' in camera_url:
                camera_url = camera_url.replace('http://', f'http://{username}:{password}@')
        
        cap = cv2.VideoCapture(camera_url)
        if cap.isOpened():
            cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # Reduce latency
            self.logger.info(f"Connected to camera: {camera_url}")
            return cap
        else:
            self.logger.error(f"Failed to connect to camera: {camera_url}")
            return None
    
    def process_camera_feed(self, 
                          camera_url: str,
                          username: str = None,
                          password: str = None,
                          alert_callback: Callable = None,
                          frame_skip: int = 3,
                          display: bool = True) -> None:
        """Process live camera feed"""
        
        cap = self.connect_camera(camera_url, username, password)
        if cap is None:
            return
        
        frame_count = 0
        threat_count = 0
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    self.logger.warning("Lost camera connection, attempting reconnect...")
                    cap.release()
                    time.sleep(5)
                    cap = self.connect_camera(camera_url, username, password)
                    if cap is None:
                        break
                    continue
                
                frame_count += 1
                
                # Skip frames for performance
                if frame_count % frame_skip != 0:
                    continue
                
                # Detect threats
                result = self.detector.predict_image(frame)
                
                if result['is_threat'] and result['confidence'] > self.detector.confidence_threshold:
                    threat_count += 1
                    self.logger.warning(f"THREAT #{threat_count}: {result['confidence']:.2%} confidence")
                    
                    if alert_callback:
                        alert_callback(frame, result, {
                            'timestamp': datetime.now(),
                            'frame_number': frame_count,
                            'threat_number': threat_count
                        })
                
                # Display frame
                if display:
                    self._display_frame(frame, result, frame_count, threat_count)
                    
                    if cv2.waitKey(1) & 0xFF == ord('q'):
                        break
        
        except KeyboardInterrupt:
            self.logger.info("Processing stopped by user")
        
        finally:
            cap.release()
            if display:
                cv2.destroyAllWindows()
    
    def _display_frame(self, frame, result, frame_count, threat_count):
        """Display frame with overlay information"""
        if result['is_threat']:
            color = (0, 0, 255)  # Red
            text = f"THREAT: {result['confidence']:.2%}"
        else:
            color = (0, 255, 0)  # Green
            text = f"NORMAL: {result['confidence']:.2%}"
        
        cv2.putText(frame, text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, color, 2)
        cv2.putText(frame, f"Frame: {frame_count}", (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(frame, f"Threats: {threat_count}", (10, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        cv2.imshow('CCTV Threat Detection', frame)
