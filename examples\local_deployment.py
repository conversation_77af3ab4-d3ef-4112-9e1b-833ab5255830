#!/usr/bin/env python3
"""
Local deployment example for CCTV Threat Detection system.

This script demonstrates how to set up a local deployment with multiple cameras,
alert handling, and logging for production use.
"""

import os
import sys
import json
import yaml
import logging
import argparse
import threading
import smtplib
from datetime import datetime
from pathlib import Path
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMult<PERSON>art
from email.mime.image import MIMEImage

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

from src.models.threat_detector import CCTVThreatDetector
from src.inference.ip_camera import IPCameraProcessor

class ThreatAlertSystem:
    """Comprehensive threat alert and logging system"""
    
    def __init__(self, config_path: str = "configs/camera_config.json"):
        self.config = self.load_config(config_path)
        self.setup_logging()
        self.setup_directories()
        
    def load_config(self, config_path: str) -> dict:
        """Load camera and alert configuration"""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Config file not found: {config_path}")
            return self.get_default_config()
    
    def get_default_config(self) -> dict:
        """Return default configuration"""
        return {
            "cameras": [
                {
                    "id": "demo_camera",
                    "name": "Demo Camera",
                    "url": "0",  # Webcam
                    "enabled": True,
                    "location": "Demo Location",
                    "settings": {
                        "confidence_threshold": 0.7,
                        "frame_skip": 3
                    }
                }
            ],
            "global_settings": {
                "alert_cooldown": 60,
                "recording": {
                    "enabled": True,
                    "storage_path": "./recordings"
                }
            }
        }
    
    def setup_logging(self):
        """Setup logging configuration"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / 'threats.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def setup_directories(self):
        """Create necessary directories"""
        directories = [
            "logs",
            "recordings",
            "threat_detections",
            "reports"
        ]
        
        for directory in directories:
            Path(directory).mkdir(exist_ok=True)
    
    def handle_threat_detection(self, camera_info: dict):
        """Create threat detection handler for specific camera"""
        last_alert_time = {}
        
        def alert_callback(frame, result, metadata):
            if not result['is_threat']:
                return
            
            camera_id = camera_info['id']
            current_time = datetime.now()
            
            # Check alert cooldown
            cooldown = self.config['global_settings'].get('alert_cooldown', 60)
            if camera_id in last_alert_time:
                time_diff = (current_time - last_alert_time[camera_id]).seconds
                if time_diff < cooldown:
                    return
            
            last_alert_time[camera_id] = current_time
            
            # Log the threat
            self.logger.warning(
                f"THREAT DETECTED - Camera: {camera_info['name']} "
                f"({camera_info['location']}) - "
                f"Confidence: {result['confidence']:.2%}"
            )
            
            # Save threat image
            threat_filename = self.save_threat_image(frame, camera_info, current_time, result)
            
            # Send alerts
            self.send_alerts(camera_info, result, current_time, threat_filename)
            
            # Record video if enabled
            if self.config['global_settings']['recording']['enabled']:
                self.trigger_recording(camera_info, current_time)
        
        return alert_callback
    
    def save_threat_image(self, frame, camera_info: dict, timestamp: datetime, result: dict) -> str:
        """Save threat detection image"""
        import cv2
        
        threat_dir = Path("threat_detections")
        filename = f"{camera_info['id']}_{timestamp.strftime('%Y%m%d_%H%M%S')}.jpg"
        filepath = threat_dir / filename
        
        # Add overlay to image
        overlay_frame = frame.copy()
        cv2.putText(overlay_frame, f"THREAT: {result['confidence']:.2%}", 
                   (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
        cv2.putText(overlay_frame, f"Camera: {camera_info['name']}", 
                   (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(overlay_frame, timestamp.strftime('%Y-%m-%d %H:%M:%S'), 
                   (10, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        cv2.imwrite(str(filepath), overlay_frame)
        self.logger.info(f"Threat image saved: {filepath}")
        
        return str(filepath)
    
    def send_alerts(self, camera_info: dict, result: dict, timestamp: datetime, image_path: str):
        """Send various types of alerts"""
        # Console alert
        print(f"\n🚨 THREAT ALERT 🚨")
        print(f"Camera: {camera_info['name']} ({camera_info['location']})")
        print(f"Time: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Confidence: {result['confidence']:.2%}")
        print(f"Image saved: {image_path}")
        print("-" * 50)
        
        # Email alert (if configured)
        self.send_email_alert(camera_info, result, timestamp, image_path)
        
        # Webhook alert (if configured)
        self.send_webhook_alert(camera_info, result, timestamp)
    
    def send_email_alert(self, camera_info: dict, result: dict, timestamp: datetime, image_path: str):
        """Send email alert"""
        # This is a placeholder - implement with your email settings
        email_config = {
            'smtp_server': 'smtp.gmail.com',
            'smtp_port': 587,
            'sender_email': '<EMAIL>',
            'sender_password': 'your-app-password',
            'recipient_emails': ['<EMAIL>']
        }
        
        try:
            msg = MIMEMultipart()
            msg['From'] = email_config['sender_email']
            msg['To'] = ', '.join(email_config['recipient_emails'])
            msg['Subject'] = f"🚨 CCTV Threat Alert - {camera_info['name']}"
            
            # Email body
            body = f"""
THREAT DETECTED

Camera: {camera_info['name']}
Location: {camera_info['location']}
Time: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}
Confidence: {result['confidence']:.2%}

Please review the attached image and take appropriate action.

This is an automated alert from the CCTV Threat Detection System.
            """
            
            msg.attach(MIMEText(body, 'plain'))
            
            # Attach image
            if os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    img_data = f.read()
                    image = MIMEImage(img_data)
                    image.add_header('Content-Disposition', 'attachment', filename=os.path.basename(image_path))
                    msg.attach(image)
            
            # Send email (commented out for demo)
            # server = smtplib.SMTP(email_config['smtp_server'], email_config['smtp_port'])
            # server.starttls()
            # server.login(email_config['sender_email'], email_config['sender_password'])
            # server.send_message(msg)
            # server.quit()
            
            self.logger.info("Email alert sent successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to send email alert: {e}")
    
    def send_webhook_alert(self, camera_info: dict, result: dict, timestamp: datetime):
        """Send webhook alert"""
        import requests
        
        webhook_url = "https://your-webhook-url.com/alerts"  # Configure your webhook
        
        payload = {
            'alert_type': 'threat_detected',
            'camera_id': camera_info['id'],
            'camera_name': camera_info['name'],
            'location': camera_info['location'],
            'confidence': result['confidence'],
            'timestamp': timestamp.isoformat()
        }
        
        try:
            # response = requests.post(webhook_url, json=payload, timeout=10)
            # response.raise_for_status()
            self.logger.info("Webhook alert sent successfully")
        except Exception as e:
            self.logger.error(f"Failed to send webhook alert: {e}")
    
    def trigger_recording(self, camera_info: dict, timestamp: datetime):
        """Trigger video recording for threat event"""
        recording_dir = Path(self.config['global_settings']['recording']['storage_path'])
        recording_dir.mkdir(exist_ok=True)
        
        filename = f"{camera_info['id']}_{timestamp.strftime('%Y%m%d_%H%M%S')}.mp4"
        filepath = recording_dir / filename
        
        self.logger.info(f"Recording triggered: {filepath}")
        # Implement actual recording logic here
    
    def start_monitoring(self, model_path: str):
        """Start monitoring all enabled cameras"""
        self.logger.info("Starting CCTV threat detection monitoring...")
        
        threads = []
        
        for camera in self.config['cameras']:
            if not camera['enabled']:
                continue
            
            self.logger.info(f"Starting monitoring for camera: {camera['name']}")
            
            # Create processor for this camera
            processor = IPCameraProcessor(
                model_path, 
                confidence_threshold=camera['settings'].get('confidence_threshold', 0.7)
            )
            
            # Create alert handler
            alert_callback = self.handle_threat_detection(camera)
            
            # Start monitoring in separate thread
            def monitor_camera(cam_config, proc, callback):
                try:
                    if cam_config['url'].isdigit():
                        # Webcam
                        from src.inference.realtime import RealtimeProcessor
                        realtime_proc = RealtimeProcessor(model_path)
                        realtime_proc.process_webcam(
                            camera_id=int(cam_config['url']),
                            alert_callback=callback,
                            display=False
                        )
                    else:
                        # IP Camera
                        proc.process_camera_feed(
                            camera_url=cam_config['url'],
                            username=cam_config.get('username'),
                            password=cam_config.get('password'),
                            alert_callback=callback,
                            frame_skip=cam_config['settings'].get('frame_skip', 3),
                            display=False
                        )
                except Exception as e:
                    self.logger.error(f"Error monitoring camera {cam_config['name']}: {e}")
            
            thread = threading.Thread(
                target=monitor_camera,
                args=(camera, processor, alert_callback),
                daemon=True
            )
            thread.start()
            threads.append(thread)
        
        if not threads:
            self.logger.warning("No cameras enabled for monitoring")
            return
        
        self.logger.info(f"Monitoring started for {len(threads)} cameras")
        
        try:
            # Keep main thread alive
            while True:
                import time
                time.sleep(1)
        except KeyboardInterrupt:
            self.logger.info("Monitoring stopped by user")

def main():
    parser = argparse.ArgumentParser(description="CCTV Threat Detection - Local Deployment")
    parser.add_argument("--model", default="your-username/cctv-threat-detection",
                       help="Model path or Hugging Face model name")
    parser.add_argument("--config", default="configs/camera_config.json",
                       help="Camera configuration file")
    parser.add_argument("--camera", help="Single camera URL for quick testing")
    
    args = parser.parse_args()
    
    print("🔍 CCTV Threat Detection - Local Deployment")
    print("=" * 50)
    
    # Quick single camera mode
    if args.camera:
        print(f"Quick camera mode: {args.camera}")
        
        processor = IPCameraProcessor(args.model)
        
        def simple_alert(frame, result, metadata):
            if result['is_threat']:
                timestamp = datetime.now()
                print(f"🚨 THREAT DETECTED: {result['confidence']:.2%} at {timestamp}")
        
        try:
            if args.camera.isdigit():
                # Webcam
                from src.inference.realtime import RealtimeProcessor
                realtime_proc = RealtimeProcessor(args.model)
                realtime_proc.process_webcam(
                    camera_id=int(args.camera),
                    alert_callback=simple_alert,
                    display=True
                )
            else:
                # IP Camera
                processor.process_camera_feed(
                    camera_url=args.camera,
                    alert_callback=simple_alert,
                    display=True
                )
        except KeyboardInterrupt:
            print("Monitoring stopped")
        
        return
    
    # Full deployment mode
    alert_system = ThreatAlertSystem(args.config)
    alert_system.start_monitoring(args.model)

if __name__ == "__main__":
    main()
