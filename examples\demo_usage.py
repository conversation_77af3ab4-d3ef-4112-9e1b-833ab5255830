#!/usr/bin/env python3
"""
Demo usage examples for CCTV Threat Detection system.

This script demonstrates various ways to use the threat detection model
for different scenarios including single images, batch processing, and video analysis.
"""

import os
import sys
import argparse
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

from src.models.threat_detector import CCTVThreatDetector
from src.inference.realtime import RealtimeProcessor
from src.inference.ip_camera import IPCameraProcessor

def demo_single_image(model_path: str, image_path: str):
    """Demonstrate single image threat detection"""
    print("🔍 Single Image Analysis Demo")
    print("=" * 40)
    
    # Load detector
    detector = CCTVThreatDetector.from_pretrained(model_path)
    
    # Analyze image
    result = detector.predict_image(image_path)
    
    # Display results
    status = "🚨 THREAT DETECTED" if result['is_threat'] else "✅ Normal Activity"
    print(f"Image: {image_path}")
    print(f"Result: {status}")
    print(f"Confidence: {result['confidence']:.2%}")
    print(f"Probabilities: Normal={result['probabilities'][0]:.3f}, Threat={result['probabilities'][1]:.3f}")
    print()

def demo_batch_processing(model_path: str, image_dir: str):
    """Demonstrate batch image processing"""
    print("📁 Batch Processing Demo")
    print("=" * 40)
    
    # Get all image files
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp'}
    image_paths = []
    
    for ext in image_extensions:
        image_paths.extend(Path(image_dir).glob(f"*{ext}"))
        image_paths.extend(Path(image_dir).glob(f"*{ext.upper()}"))
    
    if not image_paths:
        print(f"No images found in {image_dir}")
        return
    
    # Process batch
    processor = RealtimeProcessor(model_path)
    results = processor.process_image_batch([str(p) for p in image_paths])
    
    # Display results
    threat_count = 0
    for i, result in enumerate(results):
        if 'error' in result:
            print(f"❌ {image_paths[i].name}: Error - {result['error']}")
        else:
            status = "🚨 THREAT" if result['is_threat'] else "✅ NORMAL"
            print(f"{status} {image_paths[i].name}: {result['confidence']:.2%}")
            if result['is_threat']:
                threat_count += 1
    
    print(f"\nSummary: {threat_count}/{len(results)} threats detected")
    print()

def demo_video_analysis(model_path: str, video_path: str):
    """Demonstrate video analysis"""
    print("🎥 Video Analysis Demo")
    print("=" * 40)
    
    processor = RealtimeProcessor(model_path)
    
    # Process video
    print(f"Analyzing video: {video_path}")
    results = processor.process_video_file(video_path, frame_skip=30)
    
    # Find threats
    threats = [r for r in results if r['is_threat']]
    
    print(f"Processed {len(results)} frames")
    print(f"Found {len(threats)} threat detections")
    
    if threats:
        print("\nThreat Timeline:")
        for threat in threats[:10]:  # Show first 10
            print(f"  {threat['timestamp']:.1f}s (frame {threat['frame_number']}): "
                  f"{threat['confidence']:.2%} confidence")
        
        if len(threats) > 10:
            print(f"  ... and {len(threats) - 10} more")
    
    print()

def demo_webcam_monitoring(model_path: str):
    """Demonstrate webcam monitoring"""
    print("📹 Webcam Monitoring Demo")
    print("=" * 40)
    print("Starting webcam monitoring... Press 'q' to quit")
    
    def alert_callback(frame, result, metadata):
        if result['is_threat']:
            print(f"🚨 ALERT: Threat detected with {result['confidence']:.2%} confidence")
    
    processor = RealtimeProcessor(model_path)
    
    try:
        processor.process_webcam(
            camera_id=0,
            alert_callback=alert_callback,
            display=True
        )
    except KeyboardInterrupt:
        print("Monitoring stopped by user")
    except Exception as e:
        print(f"Error: {e}")
    
    print()

def demo_ip_camera_monitoring(model_path: str, camera_url: str):
    """Demonstrate IP camera monitoring"""
    print("📡 IP Camera Monitoring Demo")
    print("=" * 40)
    print(f"Connecting to camera: {camera_url}")
    print("Press 'q' to quit")
    
    def alert_callback(frame, result, metadata):
        if result['is_threat']:
            timestamp = metadata['timestamp']
            print(f"🚨 ALERT at {timestamp}: {result['confidence']:.2%} confidence")
    
    processor = IPCameraProcessor(model_path)
    
    try:
        processor.process_camera_feed(
            camera_url=camera_url,
            alert_callback=alert_callback,
            display=True
        )
    except KeyboardInterrupt:
        print("Monitoring stopped by user")
    except Exception as e:
        print(f"Error: {e}")
    
    print()

def main():
    parser = argparse.ArgumentParser(description="CCTV Threat Detection Demo")
    parser.add_argument("--model", default="your-username/cctv-threat-detection",
                       help="Model path or Hugging Face model name")
    parser.add_argument("--demo", choices=['image', 'batch', 'video', 'webcam', 'ipcam', 'all'],
                       default='all', help="Demo to run")
    parser.add_argument("--image", help="Path to image file (for image demo)")
    parser.add_argument("--image-dir", help="Path to image directory (for batch demo)")
    parser.add_argument("--video", help="Path to video file (for video demo)")
    parser.add_argument("--camera-url", help="IP camera URL (for IP camera demo)")
    
    args = parser.parse_args()
    
    print("🔍 CCTV Threat Detection - Demo Usage")
    print("=" * 50)
    print(f"Model: {args.model}")
    print()
    
    # Run demos based on selection
    if args.demo in ['image', 'all']:
        image_path = args.image or "examples/sample_images/normal_activity.jpg"
        if os.path.exists(image_path):
            demo_single_image(args.model, image_path)
        else:
            print(f"⚠️  Image not found: {image_path}")
            print("   Please provide --image path or create examples/sample_images/")
            print()
    
    if args.demo in ['batch', 'all']:
        image_dir = args.image_dir or "examples/sample_images"
        if os.path.exists(image_dir):
            demo_batch_processing(args.model, image_dir)
        else:
            print(f"⚠️  Image directory not found: {image_dir}")
            print("   Please provide --image-dir path or create examples/sample_images/")
            print()
    
    if args.demo in ['video', 'all']:
        video_path = args.video or "examples/sample_video.mp4"
        if os.path.exists(video_path):
            demo_video_analysis(args.model, video_path)
        else:
            print(f"⚠️  Video not found: {video_path}")
            print("   Please provide --video path")
            print()
    
    if args.demo == 'webcam':
        demo_webcam_monitoring(args.model)
    
    if args.demo == 'ipcam':
        camera_url = args.camera_url or "rtsp://demo:<EMAIL>:5541/onvif-media/media.amp"
        demo_ip_camera_monitoring(args.model, camera_url)
    
    print("✅ Demo completed!")

if __name__ == "__main__":
    main()
