import torch
import numpy as np
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report
import cv2
from PIL import Image

class ModelUtils:
    """Utility functions for model operations and analysis"""
    
    @staticmethod
    def calculate_metrics(y_true: List[int], y_pred: List[int], y_prob: List[float] = None) -> Dict:
        """Calculate comprehensive model metrics"""
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
        
        metrics = {
            'accuracy': accuracy_score(y_true, y_pred),
            'precision': precision_score(y_true, y_pred),
            'recall': recall_score(y_true, y_pred),
            'f1_score': f1_score(y_true, y_pred)
        }
        
        if y_prob is not None:
            metrics['auc_roc'] = roc_auc_score(y_true, y_prob)
        
        return metrics
    
    @staticmethod
    def plot_confusion_matrix(y_true: List[int], y_pred: List[int], 
                            class_names: List[str] = None, 
                            save_path: str = None) -> plt.Figure:
        """Plot confusion matrix"""
        if class_names is None:
            class_names = ['Normal', 'Threat']
        
        cm = confusion_matrix(y_true, y_pred)
        
        fig, ax = plt.subplots(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=class_names, yticklabels=class_names, ax=ax)
        ax.set_xlabel('Predicted')
        ax.set_ylabel('Actual')
        ax.set_title('Confusion Matrix')
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    @staticmethod
    def plot_prediction_distribution(predictions: List[Dict], 
                                   save_path: str = None) -> plt.Figure:
        """Plot distribution of prediction confidences"""
        threat_confidences = [p['confidence'] for p in predictions if p['is_threat']]
        normal_confidences = [p['confidence'] for p in predictions if not p['is_threat']]
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # Histogram of confidences
        ax1.hist(threat_confidences, alpha=0.7, label='Threat', bins=20, color='red')
        ax1.hist(normal_confidences, alpha=0.7, label='Normal', bins=20, color='green')
        ax1.set_xlabel('Confidence')
        ax1.set_ylabel('Frequency')
        ax1.set_title('Prediction Confidence Distribution')
        ax1.legend()
        
        # Box plot
        data = [normal_confidences, threat_confidences]
        ax2.boxplot(data, labels=['Normal', 'Threat'])
        ax2.set_ylabel('Confidence')
        ax2.set_title('Confidence by Class')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    @staticmethod
    def create_prediction_report(predictions: List[Dict], 
                               ground_truth: List[int] = None) -> str:
        """Create a detailed prediction report"""
        total_predictions = len(predictions)
        threat_predictions = sum(1 for p in predictions if p['is_threat'])
        normal_predictions = total_predictions - threat_predictions
        
        avg_threat_confidence = np.mean([p['confidence'] for p in predictions if p['is_threat']]) if threat_predictions > 0 else 0
        avg_normal_confidence = np.mean([p['confidence'] for p in predictions if not p['is_threat']]) if normal_predictions > 0 else 0
        
        report = f"""
CCTV Threat Detection Report
============================

Total Predictions: {total_predictions}
Threat Detections: {threat_predictions} ({threat_predictions/total_predictions*100:.1f}%)
Normal Activity: {normal_predictions} ({normal_predictions/total_predictions*100:.1f}%)

Average Confidence:
- Threat Predictions: {avg_threat_confidence:.3f}
- Normal Predictions: {avg_normal_confidence:.3f}
"""
        
        if ground_truth is not None:
            y_pred = [1 if p['is_threat'] else 0 for p in predictions]
            metrics = ModelUtils.calculate_metrics(ground_truth, y_pred)
            
            report += f"""
Performance Metrics:
- Accuracy: {metrics['accuracy']:.3f}
- Precision: {metrics['precision']:.3f}
- Recall: {metrics['recall']:.3f}
- F1-Score: {metrics['f1_score']:.3f}
"""
        
        return report
    
    @staticmethod
    def preprocess_for_visualization(image: np.ndarray, 
                                   target_size: Tuple[int, int] = (224, 224)) -> np.ndarray:
        """Preprocess image for visualization"""
        if len(image.shape) == 3 and image.shape[2] == 3:
            # Convert BGR to RGB if needed
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Resize image
        image = cv2.resize(image, target_size)
        
        return image
    
    @staticmethod
    def create_prediction_overlay(image: np.ndarray, 
                                prediction: Dict,
                                font_scale: float = 1.0) -> np.ndarray:
        """Create image with prediction overlay"""
        overlay_image = image.copy()
        
        if prediction['is_threat']:
            color = (255, 0, 0)  # Red for threat
            text = f"THREAT: {prediction['confidence']:.2%}"
        else:
            color = (0, 255, 0)  # Green for normal
            text = f"NORMAL: {prediction['confidence']:.2%}"
        
        # Add text overlay
        cv2.putText(overlay_image, text, (10, 30), 
                   cv2.FONT_HERSHEY_SIMPLEX, font_scale, color, 2)
        
        # Add confidence bar
        bar_width = int(200 * prediction['confidence'])
        cv2.rectangle(overlay_image, (10, 50), (10 + bar_width, 70), color, -1)
        cv2.rectangle(overlay_image, (10, 50), (210, 70), (255, 255, 255), 2)
        
        return overlay_image
    
    @staticmethod
    def save_model_summary(model, save_path: str):
        """Save model architecture summary"""
        with open(save_path, 'w') as f:
            f.write("CCTV Threat Detection Model Summary\n")
            f.write("=" * 40 + "\n\n")
            
            # Model info
            f.write(f"Model Type: {type(model).__name__}\n")
            f.write(f"Device: {next(model.parameters()).device}\n")
            
            # Parameter count
            total_params = sum(p.numel() for p in model.parameters())
            trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
            
            f.write(f"Total Parameters: {total_params:,}\n")
            f.write(f"Trainable Parameters: {trainable_params:,}\n")
            f.write(f"Non-trainable Parameters: {total_params - trainable_params:,}\n\n")
            
            # Model architecture
            f.write("Model Architecture:\n")
            f.write("-" * 20 + "\n")
            f.write(str(model))
