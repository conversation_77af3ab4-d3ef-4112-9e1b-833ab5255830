# Training Configuration for CCTV Threat Detection

# Dataset Configuration
dataset:
  name: "violence_detection"
  train_split: 0.8
  val_split: 0.1
  test_split: 0.1
  data_dir: "./data"
  cache_dir: "./cache"
  
  # Data augmentation
  augmentation:
    horizontal_flip: 0.5
    vertical_flip: 0.0
    rotation: 15
    brightness: 0.2
    contrast: 0.2
    saturation: 0.1
    hue: 0.05
    gaussian_blur: 0.1
    random_crop: true
    center_crop: false

# Model Configuration
model:
  base_model: "google/vit-base-patch16-224-in21k"
  num_classes: 2
  dropout: 0.1
  freeze_backbone: false
  freeze_layers: 0  # Number of layers to freeze from the beginning

# Training Parameters
training:
  batch_size: 16
  gradient_accumulation_steps: 2
  effective_batch_size: 32  # batch_size * gradient_accumulation_steps
  
  # Optimizer
  optimizer: "adamw"
  learning_rate: 2e-5
  weight_decay: 0.01
  adam_beta1: 0.9
  adam_beta2: 0.999
  adam_epsilon: 1e-8
  
  # Learning rate scheduler
  scheduler: "linear"
  warmup_steps: 500
  warmup_ratio: 0.1
  
  # Training duration
  num_epochs: 10
  max_steps: -1  # -1 means use num_epochs
  
  # Evaluation
  eval_strategy: "steps"
  eval_steps: 500
  eval_accumulation_steps: 1
  
  # Saving
  save_strategy: "steps"
  save_steps: 1000
  save_total_limit: 3
  load_best_model_at_end: true
  metric_for_best_model: "eval_f1"
  greater_is_better: true
  
  # Logging
  logging_steps: 100
  logging_strategy: "steps"
  report_to: ["wandb", "tensorboard"]
  
  # Early stopping
  early_stopping_patience: 3
  early_stopping_threshold: 0.001

# Hardware Configuration
hardware:
  device: "auto"  # auto, cpu, cuda
  mixed_precision: true
  dataloader_num_workers: 4
  dataloader_pin_memory: true
  gradient_checkpointing: false

# Regularization
regularization:
  label_smoothing: 0.1
  dropout: 0.1
  stochastic_depth: 0.1

# Loss Configuration
loss:
  type: "cross_entropy"
  class_weights: [1.0, 1.0]  # [normal, threat]
  focal_loss:
    enabled: false
    alpha: 0.25
    gamma: 2.0

# Metrics to track
metrics:
  - "accuracy"
  - "precision"
  - "recall"
  - "f1"
  - "auc"
  - "confusion_matrix"

# Experiment tracking
experiment:
  name: "cctv_threat_detection_v1"
  description: "Fine-tuning ViT for CCTV threat detection"
  tags: ["computer-vision", "security", "violence-detection"]
  
  wandb:
    project: "cctv-threat-detection"
    entity: "your-username"
    notes: "Baseline ViT model for threat detection"
  
  tensorboard:
    log_dir: "./logs/tensorboard"

# Checkpointing
checkpointing:
  resume_from_checkpoint: null
  save_safetensors: true
  
# Data loading
data_loading:
  image_size: 224
  interpolation: "bicubic"
  mean: [0.485, 0.456, 0.406]  # ImageNet mean
  std: [0.229, 0.224, 0.225]   # ImageNet std
  
# Validation
validation:
  batch_size: 32
  shuffle: false
  drop_last: false

# Testing
testing:
  batch_size: 32
  save_predictions: true
  prediction_file: "./results/test_predictions.json"
  
# Model export
export:
  formats: ["pytorch", "onnx", "torchscript"]
  optimize_for_inference: true
  quantization: false
