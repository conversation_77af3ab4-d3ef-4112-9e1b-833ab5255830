#!/usr/bin/env python3
"""
Basic tests for CCTV Threat Detection model.

This module contains unit tests for the core functionality of the threat detection system.
"""

import unittest
import sys
import numpy as np
from pathlib import Path
from PIL import Image
import torch

# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

from src.models.threat_detector import CCTVThreatDetector
from src.models.utils import ModelUtils

class TestCCTVThreatDetector(unittest.TestCase):
    """Test cases for CCTVThreatDetector class"""
    
    @classmethod
    def setUpClass(cls):
        """Set up test fixtures before running tests"""
        cls.model_path = "google/vit-base-patch16-224-in21k"  # Use base model for testing
        cls.test_image_size = (224, 224, 3)
    
    def setUp(self):
        """Set up test fixtures before each test method"""
        # Create a dummy detector for testing (without loading actual model)
        self.detector = CCTVThreatDetector()
    
    def create_test_image(self, size=(224, 224)) -> Image.Image:
        """Create a test image for testing purposes"""
        # Create random RGB image
        image_array = np.random.randint(0, 255, (*size, 3), dtype=np.uint8)
        return Image.fromarray(image_array)
    
    def test_detector_initialization(self):
        """Test detector initialization"""
        detector = CCTVThreatDetector()
        self.assertIsNone(detector.model)
        self.assertIsNone(detector.processor)
        self.assertEqual(detector.confidence_threshold, 0.7)
        self.assertIsNotNone(detector.device)
    
    def test_detector_with_threshold(self):
        """Test detector initialization with custom threshold"""
        threshold = 0.8
        detector = CCTVThreatDetector(confidence_threshold=threshold)
        self.assertEqual(detector.confidence_threshold, threshold)
    
    def test_preprocess_image_pil(self):
        """Test image preprocessing with PIL Image"""
        if self.detector.processor is None:
            self.skipTest("Model not loaded - skipping preprocessing test")
        
        test_image = self.create_test_image()
        try:
            processed = self.detector.preprocess_image(test_image)
            self.assertIsInstance(processed, dict)
            self.assertIn('pixel_values', processed)
        except Exception as e:
            self.skipTest(f"Preprocessing test skipped: {e}")
    
    def test_preprocess_image_numpy(self):
        """Test image preprocessing with numpy array"""
        if self.detector.processor is None:
            self.skipTest("Model not loaded - skipping preprocessing test")
        
        # Create numpy array (BGR format like OpenCV)
        image_array = np.random.randint(0, 255, self.test_image_size, dtype=np.uint8)
        
        try:
            processed = self.detector.preprocess_image(image_array)
            self.assertIsInstance(processed, dict)
            self.assertIn('pixel_values', processed)
        except Exception as e:
            self.skipTest(f"Preprocessing test skipped: {e}")
    
    def test_preprocess_image_invalid_type(self):
        """Test image preprocessing with invalid input type"""
        with self.assertRaises(ValueError):
            self.detector.preprocess_image("invalid_input")
    
    def test_predict_image_without_model(self):
        """Test prediction without loaded model"""
        test_image = self.create_test_image()
        
        with self.assertRaises(ValueError) as context:
            self.detector.predict_image(test_image)
        
        self.assertIn("Model not loaded", str(context.exception))
    
    def test_predict_batch_empty_list(self):
        """Test batch prediction with empty list"""
        results = self.detector.predict_batch([])
        self.assertEqual(len(results), 0)
    
    def test_predict_batch_with_errors(self):
        """Test batch prediction with invalid inputs"""
        invalid_inputs = ["nonexistent_file.jpg", None, 123]
        results = self.detector.predict_batch(invalid_inputs)
        
        self.assertEqual(len(results), len(invalid_inputs))
        for result in results:
            self.assertIn('error', result)
            self.assertFalse(result['is_threat'])
            self.assertEqual(result['confidence'], 0.0)

class TestModelUtils(unittest.TestCase):
    """Test cases for ModelUtils class"""
    
    def test_calculate_metrics_basic(self):
        """Test basic metrics calculation"""
        y_true = [0, 0, 1, 1, 0, 1]
        y_pred = [0, 1, 1, 1, 0, 0]
        
        metrics = ModelUtils.calculate_metrics(y_true, y_pred)
        
        self.assertIn('accuracy', metrics)
        self.assertIn('precision', metrics)
        self.assertIn('recall', metrics)
        self.assertIn('f1_score', metrics)
        
        # Check that all metrics are between 0 and 1
        for metric_name, value in metrics.items():
            if metric_name != 'auc_roc':  # AUC might not be present
                self.assertGreaterEqual(value, 0.0)
                self.assertLessEqual(value, 1.0)
    
    def test_calculate_metrics_with_probabilities(self):
        """Test metrics calculation with probabilities"""
        y_true = [0, 0, 1, 1]
        y_pred = [0, 1, 1, 1]
        y_prob = [0.1, 0.8, 0.9, 0.7]
        
        metrics = ModelUtils.calculate_metrics(y_true, y_pred, y_prob)
        
        self.assertIn('auc_roc', metrics)
        self.assertGreaterEqual(metrics['auc_roc'], 0.0)
        self.assertLessEqual(metrics['auc_roc'], 1.0)
    
    def test_create_prediction_report_basic(self):
        """Test prediction report creation"""
        predictions = [
            {'is_threat': False, 'confidence': 0.8},
            {'is_threat': True, 'confidence': 0.9},
            {'is_threat': False, 'confidence': 0.7},
            {'is_threat': True, 'confidence': 0.85}
        ]
        
        report = ModelUtils.create_prediction_report(predictions)
        
        self.assertIsInstance(report, str)
        self.assertIn("Total Predictions: 4", report)
        self.assertIn("Threat Detections: 2", report)
        self.assertIn("Normal Activity: 2", report)
    
    def test_create_prediction_report_with_ground_truth(self):
        """Test prediction report with ground truth"""
        predictions = [
            {'is_threat': False, 'confidence': 0.8},
            {'is_threat': True, 'confidence': 0.9},
            {'is_threat': False, 'confidence': 0.7},
            {'is_threat': True, 'confidence': 0.85}
        ]
        ground_truth = [0, 1, 0, 1]
        
        report = ModelUtils.create_prediction_report(predictions, ground_truth)
        
        self.assertIn("Performance Metrics:", report)
        self.assertIn("Accuracy:", report)
        self.assertIn("Precision:", report)
        self.assertIn("Recall:", report)
        self.assertIn("F1-Score:", report)
    
    def test_preprocess_for_visualization(self):
        """Test image preprocessing for visualization"""
        # Create test image
        image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        processed = ModelUtils.preprocess_for_visualization(image)
        
        self.assertEqual(processed.shape, (224, 224, 3))
        self.assertEqual(processed.dtype, np.uint8)
    
    def test_create_prediction_overlay(self):
        """Test prediction overlay creation"""
        image = np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)
        prediction = {'is_threat': True, 'confidence': 0.85}
        
        overlay = ModelUtils.create_prediction_overlay(image, prediction)
        
        self.assertEqual(overlay.shape, image.shape)
        self.assertEqual(overlay.dtype, image.dtype)
        # The overlay should be different from original (text added)
        self.assertFalse(np.array_equal(overlay, image))

class TestIntegration(unittest.TestCase):
    """Integration tests for the complete system"""
    
    def test_end_to_end_workflow_mock(self):
        """Test end-to-end workflow with mocked components"""
        # This test would require actual model loading, so we'll mock it
        detector = CCTVThreatDetector()
        
        # Test that the workflow structure is correct
        self.assertIsNotNone(detector.device)
        self.assertEqual(detector.confidence_threshold, 0.7)
        
        # Test error handling
        with self.assertRaises(ValueError):
            detector.predict_image("test.jpg")

def create_test_suite():
    """Create a test suite with all test cases"""
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTest(unittest.makeSuite(TestCCTVThreatDetector))
    suite.addTest(unittest.makeSuite(TestModelUtils))
    suite.addTest(unittest.makeSuite(TestIntegration))
    
    return suite

def run_tests():
    """Run all tests"""
    # Create test suite
    suite = create_test_suite()
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Return success status
    return result.wasSuccessful()

if __name__ == '__main__':
    # Run tests when script is executed directly
    success = run_tests()
    
    if success:
        print("\n✅ All tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)
