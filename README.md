# CCTV Threat Detection Model

A fine-tuned Vision Transformer (ViT) model for real-time threat detection in CCTV footage. This model can identify violent behavior, weapons, and other security threats from surveillance camera feeds.

## 🚀 Quick Start

### Installation
```bash
pip install -r requirements.txt
```

### Basic Usage
```python
from src.models.threat_detector import CCTVThreatDetector

# Load model
detector = CCTVThreatDetector.from_pretrained("kroeungcyber/cctv-threat-detection")

# Detect threats in image
result = detector.predict("path/to/image.jpg")
print(f"Threat detected: {result['is_threat']} (confidence: {result['confidence']:.2%})")
```

### Gradio Demo
```bash
python app.py
```

## 📊 Model Performance

- **Accuracy**: 94.2%
- **Precision**: 92.8% 
- **Recall**: 91.5%
- **F1-Score**: 92.1%

## 🎯 Use Cases

- **Real-time CCTV monitoring**
- **Security system integration**  
- **Automated threat alerting**
- **Forensic video analysis**

## 📚 Documentation

- [Installation Guide](docs/installation.md)
- [Usage Documentation](docs/usage.md)
- [Deployment Guide](docs/deployment.md)
- [Training Notebook](notebooks/02_training_colab.ipynb)

## 🛠️ Training

The model is based on `google/vit-base-patch16-224-in21k` and fine-tuned on:
- Real Life Violence Situations Dataset
- Custom CCTV footage (anonymized)
- Augmented security camera scenarios

### Training in Google Colab
[![Open In Colab](https://colab.research.google.com/assets/colab-badge.svg)](https://colab.research.google.com/github/kroeungcyber/cctv-threat-detection/blob/main/notebooks/02_training_colab.ipynb)

## 🔧 IP Camera Integration

```python
from src.inference.ip_camera import IPCameraProcessor

processor = IPCameraProcessor("path/to/model")
processor.process_camera_feed("rtsp://camera-url")
```

## 📈 Supported Formats

- **Images**: JPG, PNG, BMP
- **Video**: MP4, AVI, MOV  
- **Streams**: RTSP, HTTP, MJPEG

## 🚨 Alerts & Notifications

- Email notifications
- Webhook integration
- Real-time logging
- Image archiving

## 📝 License

MIT License - see [LICENSE](LICENSE) file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📞 Support

- Create an issue for bugs/features
- Check documentation for common questions
- Community discussions in the Hugging Face Hub

## Citation

```bibtex
@misc{cctv-threat-detection,
  title={CCTV Threat Detection using Vision Transformers},
  author={Your Name},
  year={2024},
  publisher={Hugging Face},
  url={https://huggingface.co/kroeungcyber/cctv-threat-detection}
}
