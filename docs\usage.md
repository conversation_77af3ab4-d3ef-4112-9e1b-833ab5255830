# Usage Guide

This guide covers how to use the CCTV Threat Detection system for various scenarios.

## Quick Start

### 1. Basic Image Analysis

```python
from src.models.threat_detector import CCTVThreatDetector

# Load the model
detector = CCTVThreatDetector.from_pretrained("your-username/cctv-threat-detection")

# Analyze a single image
result = detector.predict_image("path/to/image.jpg")
print(f"Threat detected: {result['is_threat']}")
print(f"Confidence: {result['confidence']:.2%}")
```

### 2. Launch Gradio Demo

```bash
python app.py
```

Then open your browser to `http://localhost:7860`

## Detailed Usage

### Image Analysis

#### Single Image Prediction

```python
from src.models.threat_detector import CCTVThreatDetector
from PIL import Image

# Initialize detector
detector = CCTVThreatDetector.from_pretrained("your-username/cctv-threat-detection")

# Method 1: From file path
result = detector.predict_image("surveillance_image.jpg")

# Method 2: From PIL Image
image = Image.open("surveillance_image.jpg")
result = detector.predict_image(image)

# Method 3: From numpy array
import cv2
image = cv2.imread("surveillance_image.jpg")
result = detector.predict_image(image)

# Access results
print(f"Is threat: {result['is_threat']}")
print(f"Confidence: {result['confidence']:.3f}")
print(f"Probabilities: {result['probabilities']}")
```

#### Batch Image Processing

```python
from src.inference.realtime import RealtimeProcessor

# Initialize processor
processor = RealtimeProcessor("your-username/cctv-threat-detection")

# Process multiple images
image_paths = ["image1.jpg", "image2.jpg", "image3.jpg"]
results = processor.process_image_batch(image_paths)

# Print results
for i, result in enumerate(results):
    print(f"Image {i+1}: {'THREAT' if result['is_threat'] else 'NORMAL'} "
          f"({result['confidence']:.2%})")
```

### Video Analysis

#### Process Video File

```python
from src.inference.realtime import RealtimeProcessor

# Initialize processor
processor = RealtimeProcessor("your-username/cctv-threat-detection")

# Process video file
results = processor.process_video_file(
    video_path="surveillance_video.mp4",
    output_path="analyzed_video.mp4",  # Optional: save with overlays
    frame_skip=30  # Process every 30th frame
)

# Find threat detections
threats = [r for r in results if r['is_threat']]
print(f"Found {len(threats)} threats in video")

for threat in threats:
    print(f"Threat at {threat['timestamp']:.1f}s "
          f"(frame {threat['frame_number']}) - "
          f"confidence: {threat['confidence']:.2%}")
```

### Real-time Processing

#### Webcam Processing

```python
from src.inference.realtime import RealtimeProcessor

def alert_callback(frame, result, metadata):
    """Called when a threat is detected"""
    if result['is_threat']:
        print(f"ALERT: Threat detected with {result['confidence']:.2%} confidence")
        # Save frame, send notification, etc.

# Initialize processor
processor = RealtimeProcessor("your-username/cctv-threat-detection")

# Process webcam feed (camera ID 0)
processor.process_webcam(
    camera_id=0,
    alert_callback=alert_callback,
    display=True
)
```

#### IP Camera Processing

```python
from src.inference.ip_camera import IPCameraProcessor

def threat_alert(frame, result, metadata):
    """Handle threat detection"""
    timestamp = metadata['timestamp']
    print(f"THREAT DETECTED at {timestamp}")
    
    # Save threat image
    cv2.imwrite(f"threat_{timestamp.strftime('%Y%m%d_%H%M%S')}.jpg", frame)

# Initialize processor
processor = IPCameraProcessor("your-username/cctv-threat-detection")

# Process IP camera stream
processor.process_camera_feed(
    camera_url="rtsp://*************:554/stream1",
    username="admin",
    password="password",
    alert_callback=threat_alert,
    frame_skip=3,
    display=True
)
```

### Command Line Usage

#### Real-time Processing

```bash
# Process webcam
python -m src.inference.realtime --model your-username/cctv-threat-detection --source 0

# Process video file
python -m src.inference.realtime --model your-username/cctv-threat-detection --source video.mp4

# Process with output video
python -m src.inference.realtime --model your-username/cctv-threat-detection --source video.mp4 --output analyzed.mp4

# Adjust confidence threshold
python -m src.inference.realtime --model your-username/cctv-threat-detection --source 0 --confidence 0.8
```

#### Batch Processing Script

```bash
# Process all images in a directory
python scripts/batch_process.py --input ./images --output ./results --model your-username/cctv-threat-detection
```

## Configuration

### Model Configuration

```python
# Custom confidence threshold
detector = CCTVThreatDetector.from_pretrained(
    "your-username/cctv-threat-detection",
    confidence_threshold=0.8
)

# Specify device
detector = CCTVThreatDetector.from_pretrained(
    "your-username/cctv-threat-detection",
    device="cuda"  # or "cpu"
)
```

### Using Configuration Files

```python
import yaml
from src.models.threat_detector import CCTVThreatDetector

# Load configuration
with open("config.yaml", "r") as f:
    config = yaml.safe_load(f)

# Initialize with config
detector = CCTVThreatDetector.from_pretrained(
    config["model"]["name"],
    confidence_threshold=config["model"]["confidence_threshold"]
)
```

## Advanced Usage

### Custom Alert Handling

```python
import smtplib
from email.mime.text import MIMEText
from datetime import datetime

class ThreatAlertHandler:
    def __init__(self, email_config):
        self.email_config = email_config
    
    def handle_threat(self, frame, result, metadata):
        """Comprehensive threat handling"""
        
        # 1. Log the threat
        timestamp = datetime.now()
        print(f"[{timestamp}] THREAT DETECTED: {result['confidence']:.2%}")
        
        # 2. Save evidence
        filename = f"threat_{timestamp.strftime('%Y%m%d_%H%M%S')}.jpg"
        cv2.imwrite(filename, frame)
        
        # 3. Send email alert
        self.send_email_alert(result, filename)
        
        # 4. Store in database (if configured)
        # self.store_in_database(result, filename, timestamp)
    
    def send_email_alert(self, result, filename):
        """Send email notification"""
        msg = MIMEText(f"Threat detected with {result['confidence']:.2%} confidence")
        msg['Subject'] = 'CCTV Threat Alert'
        msg['From'] = self.email_config['sender']
        msg['To'] = self.email_config['recipient']
        
        # Send email (implement SMTP logic)
        pass

# Usage
alert_handler = ThreatAlertHandler(email_config)
processor.process_camera_feed(
    camera_url="rtsp://camera-url",
    alert_callback=alert_handler.handle_threat
)
```

### Multi-Camera Setup

```python
import threading
from src.inference.ip_camera import IPCameraProcessor

def setup_multi_camera_monitoring():
    """Monitor multiple cameras simultaneously"""
    
    cameras = [
        {"url": "rtsp://*************:554/stream1", "name": "Entrance"},
        {"url": "rtsp://*************:554/stream1", "name": "Parking"},
        {"url": "rtsp://*************:554/stream1", "name": "Lobby"}
    ]
    
    threads = []
    
    for camera in cameras:
        processor = IPCameraProcessor("your-username/cctv-threat-detection")
        
        def monitor_camera(cam_info):
            def alert_callback(frame, result, metadata):
                print(f"THREAT in {cam_info['name']}: {result['confidence']:.2%}")
            
            processor.process_camera_feed(
                camera_url=cam_info["url"],
                alert_callback=alert_callback,
                display=False  # Don't display for multi-camera
            )
        
        thread = threading.Thread(target=monitor_camera, args=(camera,))
        thread.start()
        threads.append(thread)
    
    return threads

# Start monitoring
threads = setup_multi_camera_monitoring()
```

### Performance Optimization

```python
# Use threaded processing for better performance
processor = RealtimeProcessor("your-username/cctv-threat-detection")

# Start threaded processing
capture_thread, process_thread = processor.start_threaded_processing(
    input_source=0,  # webcam
    alert_callback=alert_callback
)

# Process results from queue
while processor.is_processing:
    try:
        frame, result = processor.result_queue.get(timeout=1.0)
        if result['is_threat']:
            print(f"Threat detected: {result['confidence']:.2%}")
    except queue.Empty:
        continue

# Stop processing
processor.stop_processing()
```

## Integration Examples

### Flask API Integration

```python
from flask import Flask, request, jsonify
from src.models.threat_detector import CCTVThreatDetector

app = Flask(__name__)
detector = CCTVThreatDetector.from_pretrained("your-username/cctv-threat-detection")

@app.route('/predict', methods=['POST'])
def predict():
    if 'image' not in request.files:
        return jsonify({'error': 'No image provided'}), 400
    
    file = request.files['image']
    image = Image.open(file.stream)
    
    result = detector.predict_image(image)
    
    return jsonify({
        'is_threat': result['is_threat'],
        'confidence': float(result['confidence']),
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
```

### Webhook Integration

```python
import requests

def send_webhook_alert(result, webhook_url):
    """Send threat alert to webhook"""
    payload = {
        'alert_type': 'threat_detected',
        'confidence': result['confidence'],
        'timestamp': datetime.now().isoformat(),
        'location': 'Camera_01'
    }
    
    try:
        response = requests.post(webhook_url, json=payload, timeout=10)
        response.raise_for_status()
        print("Webhook alert sent successfully")
    except requests.RequestException as e:
        print(f"Failed to send webhook: {e}")

# Use in alert callback
def alert_callback(frame, result, metadata):
    if result['is_threat']:
        send_webhook_alert(result, "https://your-webhook-url.com/alerts")
```

## Troubleshooting

### Common Issues

1. **Model Loading Issues**
   ```python
   # Check if model exists
   try:
       detector = CCTVThreatDetector.from_pretrained("model-name")
   except Exception as e:
       print(f"Model loading failed: {e}")
   ```

2. **Camera Connection Issues**
   ```python
   # Test camera connection
   import cv2
   cap = cv2.VideoCapture("rtsp://camera-url")
   if not cap.isOpened():
       print("Failed to connect to camera")
   ```

3. **Performance Issues**
   ```python
   # Reduce processing load
   processor = RealtimeProcessor(
       "model-name",
       confidence_threshold=0.8  # Higher threshold = fewer false positives
   )
   
   # Process fewer frames
   processor.process_webcam(frame_skip=5)  # Process every 5th frame
   ```

## Best Practices

1. **Confidence Thresholds**: Start with 0.7 and adjust based on your needs
2. **Frame Skipping**: Use frame_skip=3-5 for real-time processing
3. **Alert Cooldown**: Implement cooldown periods to avoid spam alerts
4. **Evidence Storage**: Always save frames when threats are detected
5. **Monitoring**: Log all detections for analysis and improvement
6. **Testing**: Test with known threat/normal videos before deployment

## Next Steps

- Explore [Training Guide](training.md) to customize the model
- Check [Deployment Guide](deployment.md) for production setup
- Review [API Documentation](api.md) for integration details
