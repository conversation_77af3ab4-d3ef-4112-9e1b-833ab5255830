from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="cctv-threat-detection",
    version="1.0.0",
    author="Your Name",
    author_email="<EMAIL>",
    description="A Vision Transformer model for real-time threat detection in CCTV footage",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/cctv-threat-detection",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Security",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "black>=22.0.0",
            "flake8>=4.0.0",
            "jupyter>=1.0.0",
        ],
        "deployment": [
            "fastapi>=0.75.0",
            "uvicorn>=0.17.0",
            "docker>=5.0.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "cctv-train=src.training.train:main",
            "cctv-inference=src.inference.realtime:main",
            "cctv-demo=app:main",
        ],
    },
)
