# CCTV Threat Detection Configuration

# Model Configuration
model:
  name: "google/vit-base-patch16-224-in21k"
  confidence_threshold: 0.7
  device: "auto"  # auto, cpu, cuda
  cache_dir: "./models"

# Data Configuration
data:
  image_size: 224
  batch_size: 16
  num_workers: 4
  augmentation:
    enabled: true
    brightness: 0.2
    contrast: 0.2
    rotation: 15

# Training Configuration
training:
  learning_rate: 2e-5
  epochs: 10
  warmup_steps: 500
  weight_decay: 0.01
  gradient_accumulation_steps: 2
  save_steps: 1000
  eval_steps: 500
  logging_steps: 100

# Inference Configuration
inference:
  frame_skip: 3
  max_batch_size: 8
  output_format: "json"
  save_predictions: true
  prediction_dir: "./predictions"

# Camera Configuration
camera:
  default_fps: 30
  buffer_size: 1
  reconnect_attempts: 5
  reconnect_delay: 5
  timeout: 30

# Alert Configuration
alerts:
  enabled: true
  email:
    enabled: false
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    sender_email: ""
    sender_password: ""
    recipient_emails: []
  webhook:
    enabled: false
    url: ""
    headers: {}
  logging:
    enabled: true
    level: "INFO"
    file: "./logs/threats.log"

# Storage Configuration
storage:
  save_threat_images: true
  threat_images_dir: "./threat_detections"
  max_storage_gb: 10
  cleanup_days: 30

# Performance Configuration
performance:
  use_gpu: true
  mixed_precision: true
  optimize_for_inference: true
  thread_count: 4
