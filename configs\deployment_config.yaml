# Deployment Configuration for CCTV Threat Detection

# Application Configuration
app:
  name: "CCTV Threat Detection"
  version: "1.0.0"
  environment: "production"  # development, staging, production
  debug: false
  host: "0.0.0.0"
  port: 7860

# Model Configuration
model:
  path: "./models/cctv-threat-detection"
  device: "auto"
  confidence_threshold: 0.7
  batch_size: 8
  max_concurrent_requests: 10
  model_cache_size: 2  # Number of models to keep in memory

# API Configuration
api:
  enabled: true
  prefix: "/api/v1"
  cors_origins: ["*"]
  rate_limiting:
    enabled: true
    requests_per_minute: 60
    burst_size: 10
  
  endpoints:
    - name: "predict_image"
      path: "/predict/image"
      methods: ["POST"]
      max_file_size: "10MB"
    
    - name: "predict_video"
      path: "/predict/video"
      methods: ["POST"]
      max_file_size: "100MB"
    
    - name: "health"
      path: "/health"
      methods: ["GET"]
    
    - name: "metrics"
      path: "/metrics"
      methods: ["GET"]

# Security Configuration
security:
  authentication:
    enabled: false
    type: "api_key"  # api_key, jwt, oauth
    api_keys: []
  
  https:
    enabled: false
    cert_file: ""
    key_file: ""
  
  input_validation:
    max_image_size: "10MB"
    max_video_size: "100MB"
    allowed_image_formats: ["jpg", "jpeg", "png", "bmp"]
    allowed_video_formats: ["mp4", "avi", "mov", "mkv"]

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  handlers:
    console:
      enabled: true
      level: "INFO"
    
    file:
      enabled: true
      level: "INFO"
      filename: "./logs/app.log"
      max_size: "10MB"
      backup_count: 5
    
    syslog:
      enabled: false
      address: "localhost:514"

# Monitoring Configuration
monitoring:
  metrics:
    enabled: true
    endpoint: "/metrics"
    include_system_metrics: true
  
  health_check:
    enabled: true
    endpoint: "/health"
    timeout: 30
  
  prometheus:
    enabled: false
    port: 9090
  
  alerts:
    enabled: false
    webhook_url: ""
    thresholds:
      error_rate: 0.05
      response_time: 5.0
      memory_usage: 0.8

# Storage Configuration
storage:
  uploads:
    path: "./uploads"
    max_size: "1GB"
    cleanup_after: 24  # hours
  
  results:
    path: "./results"
    save_predictions: true
    save_images: false
    retention_days: 7
  
  cache:
    enabled: true
    type: "memory"  # memory, redis, file
    max_size: "500MB"
    ttl: 3600  # seconds

# Database Configuration (optional)
database:
  enabled: false
  type: "sqlite"  # sqlite, postgresql, mysql
  url: "sqlite:///./app.db"
  pool_size: 5
  max_overflow: 10

# Queue Configuration (for async processing)
queue:
  enabled: false
  type: "redis"  # redis, rabbitmq, memory
  url: "redis://localhost:6379/0"
  max_workers: 4

# Docker Configuration
docker:
  image_name: "cctv-threat-detection"
  tag: "latest"
  base_image: "python:3.9-slim"
  
  ports:
    - "7860:7860"
  
  volumes:
    - "./models:/app/models"
    - "./logs:/app/logs"
    - "./uploads:/app/uploads"
  
  environment:
    - "PYTHONPATH=/app"
    - "CUDA_VISIBLE_DEVICES=0"

# Kubernetes Configuration
kubernetes:
  namespace: "default"
  replicas: 2
  
  resources:
    requests:
      cpu: "500m"
      memory: "2Gi"
    limits:
      cpu: "2000m"
      memory: "4Gi"
  
  service:
    type: "LoadBalancer"
    port: 80
    target_port: 7860
  
  ingress:
    enabled: false
    host: "cctv-detection.example.com"
    tls: false

# Performance Configuration
performance:
  workers: 4
  worker_class: "uvicorn.workers.UvicornWorker"
  max_requests: 1000
  max_requests_jitter: 100
  timeout: 30
  keepalive: 2
  
  optimization:
    enable_gzip: true
    static_file_caching: true
    response_caching: false

# Backup Configuration
backup:
  enabled: false
  schedule: "0 2 * * *"  # Daily at 2 AM
  retention_days: 30
  
  targets:
    - "./models"
    - "./logs"
    - "./configs"
  
  destination: "./backups"

# Feature Flags
features:
  gradio_interface: true
  api_interface: true
  batch_processing: true
  video_processing: true
  real_time_streaming: false
  multi_camera_support: false
