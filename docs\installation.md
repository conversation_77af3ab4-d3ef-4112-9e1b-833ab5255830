# Installation Guide

This guide will help you install and set up the CCTV Threat Detection system on your machine.

## Prerequisites

- Python 3.8 or higher
- CUDA-compatible GPU (optional, but recommended for better performance)
- Git

## Quick Installation

### 1. <PERSON>lone the Repository

```bash
git clone https://huggingface.co/your-username/cctv-threat-detection
cd cctv-threat-detection
```

### 2. Create Virtual Environment

```bash
# Using venv
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Or using conda
conda create -n cctv-detection python=3.9
conda activate cctv-detection
```

### 3. Install Dependencies

```bash
pip install -r requirements.txt
```

### 4. Install the Package

```bash
pip install -e .
```

## Detailed Installation

### System Requirements

#### Minimum Requirements
- **OS**: Windows 10, macOS 10.14+, or Linux (Ubuntu 18.04+)
- **RAM**: 8GB
- **Storage**: 5GB free space
- **Python**: 3.8+

#### Recommended Requirements
- **OS**: Linux (Ubuntu 20.04+)
- **RAM**: 16GB+
- **GPU**: NVIDIA GPU with 6GB+ VRAM
- **Storage**: 20GB+ free space
- **Python**: 3.9+

### GPU Setup (Optional)

For CUDA support:

```bash
# Check CUDA version
nvidia-smi

# Install PyTorch with CUDA support
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

### Development Installation

For development and contributing:

```bash
# Clone with development dependencies
pip install -e ".[dev]"

# Install pre-commit hooks
pre-commit install
```

## Docker Installation

### Using Docker

```bash
# Build the image
docker build -t cctv-threat-detection .

# Run the container
docker run -p 7860:7860 cctv-threat-detection
```

### Using Docker Compose

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f
```

## Verification

### Test Installation

```bash
# Test basic functionality
python -c "from src.models.threat_detector import CCTVThreatDetector; print('Installation successful!')"

# Run tests
pytest tests/

# Start demo application
python app.py
```

### Check GPU Support

```bash
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
```

## Configuration

### Basic Configuration

1. Copy the example configuration:
```bash
cp config.yaml.example config.yaml
```

2. Edit the configuration file:
```bash
nano config.yaml
```

3. Set up camera configuration (if using IP cameras):
```bash
cp configs/camera_config.json.example configs/camera_config.json
nano configs/camera_config.json
```

### Environment Variables

Create a `.env` file:

```bash
# Model configuration
MODEL_PATH=./models/cctv-threat-detection
CONFIDENCE_THRESHOLD=0.7

# Logging
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log

# Performance
CUDA_VISIBLE_DEVICES=0
OMP_NUM_THREADS=4
```

## Troubleshooting

### Common Issues

#### 1. CUDA Out of Memory
```bash
# Reduce batch size in config.yaml
model:
  batch_size: 4  # Reduce from default 16
```

#### 2. OpenCV Issues
```bash
# Install system dependencies (Ubuntu/Debian)
sudo apt-get update
sudo apt-get install libgl1-mesa-glx libglib2.0-0

# For headless servers
sudo apt-get install libglib2.0-0 libsm6 libxext6 libxrender-dev libgomp1
```

#### 3. Permission Issues
```bash
# Fix permissions
chmod +x scripts/*.py
sudo chown -R $USER:$USER ./logs ./models
```

#### 4. Port Already in Use
```bash
# Change port in config.yaml or use environment variable
export PORT=8080
python app.py
```

### Getting Help

If you encounter issues:

1. Check the [FAQ](../README.md#faq)
2. Search existing [issues](https://github.com/your-username/cctv-threat-detection/issues)
3. Create a new issue with:
   - System information
   - Error messages
   - Steps to reproduce

## Next Steps

After installation:

1. Read the [Usage Guide](usage.md)
2. Check out [Examples](../examples/)
3. Review [Deployment Guide](deployment.md)
4. Explore [Training Notebooks](../notebooks/)

## Updating

To update to the latest version:

```bash
git pull origin main
pip install -r requirements.txt --upgrade
```

## Uninstallation

To completely remove the installation:

```bash
# Remove virtual environment
deactivate
rm -rf venv/

# Remove cloned repository
cd ..
rm -rf cctv-threat-detection/
