# Deployment Guide

This guide covers how to deploy the CCTV Threat Detection system in production environments.

## Overview

The system can be deployed in several ways:
- **Local Deployment**: Single machine setup
- **Docker Deployment**: Containerized deployment
- **Cloud Deployment**: AWS, GCP, Azure
- **Edge Deployment**: On-premise edge devices
- **Kubernetes**: Scalable container orchestration

## Local Deployment

### Production Setup

```bash
# 1. Clone and setup
git clone https://huggingface.co/your-username/cctv-threat-detection
cd cctv-threat-detection
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# 2. Configure for production
cp configs/deployment_config.yaml.example configs/deployment_config.yaml
nano configs/deployment_config.yaml

# 3. Start the service
python app.py --config configs/deployment_config.yaml
```

### Service Configuration

Create a systemd service file:

```bash
sudo nano /etc/systemd/system/cctv-detection.service
```

```ini
[Unit]
Description=CCTV Threat Detection Service
After=network.target

[Service]
Type=simple
User=cctv-user
WorkingDirectory=/opt/cctv-threat-detection
Environment=PATH=/opt/cctv-threat-detection/venv/bin
ExecStart=/opt/cctv-threat-detection/venv/bin/python app.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# Enable and start service
sudo systemctl enable cctv-detection
sudo systemctl start cctv-detection
sudo systemctl status cctv-detection
```

## Docker Deployment

### Basic Docker Setup

Create `Dockerfile`:

```dockerfile
FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    git \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p logs uploads results models

# Expose port
EXPOSE 7860

# Set environment variables
ENV PYTHONPATH=/app
ENV GRADIO_SERVER_NAME=0.0.0.0

# Run the application
CMD ["python", "app.py"]
```

Build and run:

```bash
# Build image
docker build -t cctv-threat-detection .

# Run container
docker run -d \
  --name cctv-detection \
  -p 7860:7860 \
  -v $(pwd)/models:/app/models \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/configs:/app/configs \
  cctv-threat-detection
```

### Docker Compose Setup

Create `docker-compose.yml`:

```yaml
version: '3.8'

services:
  cctv-detection:
    build: .
    container_name: cctv-threat-detection
    ports:
      - "7860:7860"
    volumes:
      - ./models:/app/models
      - ./logs:/app/logs
      - ./configs:/app/configs
      - ./uploads:/app/uploads
      - ./results:/app/results
    environment:
      - PYTHONPATH=/app
      - CUDA_VISIBLE_DEVICES=0
    restart: unless-stopped
    
  nginx:
    image: nginx:alpine
    container_name: cctv-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - cctv-detection
    restart: unless-stopped

  redis:
    image: redis:alpine
    container_name: cctv-redis
    ports:
      - "6379:6379"
    restart: unless-stopped
```

### GPU Support (Docker)

For GPU support, use nvidia-docker:

```dockerfile
FROM nvidia/cuda:11.8-runtime-ubuntu20.04

# Install Python and dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    libgl1-mesa-glx \
    libglib2.0-0 \
    && rm -rf /var/lib/apt/lists/*

# Rest of Dockerfile...
```

```bash
# Run with GPU support
docker run --gpus all -d \
  --name cctv-detection-gpu \
  -p 7860:7860 \
  cctv-threat-detection
```

## Cloud Deployment

### AWS Deployment

#### EC2 Instance Setup

```bash
# 1. Launch EC2 instance (recommended: g4dn.xlarge for GPU)
# 2. Install Docker
sudo yum update -y
sudo yum install -y docker
sudo service docker start
sudo usermod -a -G docker ec2-user

# 3. Install nvidia-docker (for GPU instances)
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list

# 4. Deploy application
docker run -d \
  --name cctv-detection \
  --gpus all \
  -p 80:7860 \
  your-registry/cctv-threat-detection:latest
```

#### ECS Deployment

Create `task-definition.json`:

```json
{
  "family": "cctv-threat-detection",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "2048",
  "memory": "4096",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "cctv-detection",
      "image": "your-registry/cctv-threat-detection:latest",
      "portMappings": [
        {
          "containerPort": 7860,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "MODEL_PATH",
          "value": "your-username/cctv-threat-detection"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/cctv-threat-detection",
          "awslogs-region": "us-west-2",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

### Google Cloud Platform

#### Cloud Run Deployment

```bash
# 1. Build and push to Container Registry
gcloud builds submit --tag gcr.io/PROJECT-ID/cctv-threat-detection

# 2. Deploy to Cloud Run
gcloud run deploy cctv-threat-detection \
  --image gcr.io/PROJECT-ID/cctv-threat-detection \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --memory 4Gi \
  --cpu 2 \
  --port 7860
```

#### GKE Deployment

Create `k8s-deployment.yaml`:

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cctv-threat-detection
spec:
  replicas: 2
  selector:
    matchLabels:
      app: cctv-threat-detection
  template:
    metadata:
      labels:
        app: cctv-threat-detection
    spec:
      containers:
      - name: cctv-detection
        image: gcr.io/PROJECT-ID/cctv-threat-detection:latest
        ports:
        - containerPort: 7860
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        env:
        - name: MODEL_PATH
          value: "your-username/cctv-threat-detection"
---
apiVersion: v1
kind: Service
metadata:
  name: cctv-threat-detection-service
spec:
  selector:
    app: cctv-threat-detection
  ports:
  - port: 80
    targetPort: 7860
  type: LoadBalancer
```

## Kubernetes Deployment

### Complete K8s Setup

```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: cctv-detection

---
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: cctv-config
  namespace: cctv-detection
data:
  config.yaml: |
    model:
      confidence_threshold: 0.7
      device: "auto"
    app:
      host: "0.0.0.0"
      port: 7860

---
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cctv-threat-detection
  namespace: cctv-detection
spec:
  replicas: 3
  selector:
    matchLabels:
      app: cctv-threat-detection
  template:
    metadata:
      labels:
        app: cctv-threat-detection
    spec:
      containers:
      - name: cctv-detection
        image: your-registry/cctv-threat-detection:latest
        ports:
        - containerPort: 7860
        resources:
          requests:
            memory: "2Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        volumeMounts:
        - name: config-volume
          mountPath: /app/config.yaml
          subPath: config.yaml
        livenessProbe:
          httpGet:
            path: /health
            port: 7860
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 7860
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: config-volume
        configMap:
          name: cctv-config

---
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: cctv-threat-detection-service
  namespace: cctv-detection
spec:
  selector:
    app: cctv-threat-detection
  ports:
  - port: 80
    targetPort: 7860
  type: LoadBalancer

---
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cctv-threat-detection-ingress
  namespace: cctv-detection
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - cctv-detection.yourdomain.com
    secretName: cctv-detection-tls
  rules:
  - host: cctv-detection.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: cctv-threat-detection-service
            port:
              number: 80
```

Deploy to Kubernetes:

```bash
kubectl apply -f k8s/
```

## Edge Deployment

### NVIDIA Jetson Setup

```bash
# 1. Install JetPack SDK
# 2. Install Docker
sudo apt-get update
sudo apt-get install -y docker.io
sudo systemctl enable docker
sudo systemctl start docker

# 3. Install nvidia-docker
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list

# 4. Build ARM64 image
docker buildx build --platform linux/arm64 -t cctv-detection-arm64 .

# 5. Run on Jetson
docker run -d \
  --runtime nvidia \
  --name cctv-detection \
  -p 7860:7860 \
  cctv-detection-arm64
```

### Raspberry Pi Setup

```bash
# 1. Install Docker on Raspberry Pi
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
sudo usermod -aG docker pi

# 2. Build ARM image
docker buildx build --platform linux/arm/v7 -t cctv-detection-arm .

# 3. Run with limited resources
docker run -d \
  --name cctv-detection \
  -p 7860:7860 \
  --memory=1g \
  --cpus=2 \
  cctv-detection-arm
```

## Load Balancing & Scaling

### Nginx Configuration

Create `nginx.conf`:

```nginx
upstream cctv_backend {
    server cctv-detection-1:7860;
    server cctv-detection-2:7860;
    server cctv-detection-3:7860;
}

server {
    listen 80;
    server_name cctv-detection.yourdomain.com;

    location / {
        proxy_pass http://cctv_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support for Gradio
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

### Auto-scaling with Kubernetes

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: cctv-detection-hpa
  namespace: cctv-detection
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: cctv-threat-detection
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

## Monitoring & Logging

### Prometheus Monitoring

```yaml
# prometheus-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
    scrape_configs:
    - job_name: 'cctv-detection'
      static_configs:
      - targets: ['cctv-threat-detection-service:80']
      metrics_path: /metrics
```

### Grafana Dashboard

```json
{
  "dashboard": {
    "title": "CCTV Threat Detection Metrics",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))"
          }
        ]
      },
      {
        "title": "Threat Detection Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(threats_detected_total[1h])"
          }
        ]
      }
    ]
  }
}
```

## Security Considerations

### SSL/TLS Setup

```bash
# Using Let's Encrypt with Certbot
sudo apt-get install certbot python3-certbot-nginx
sudo certbot --nginx -d cctv-detection.yourdomain.com
```

### API Authentication

```python
# Add to app.py
from functools import wraps
import jwt

def require_auth(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        token = request.headers.get('Authorization')
        if not token:
            return jsonify({'error': 'No token provided'}), 401
        
        try:
            jwt.decode(token, app.config['SECRET_KEY'], algorithms=['HS256'])
        except jwt.InvalidTokenError:
            return jsonify({'error': 'Invalid token'}), 401
        
        return f(*args, **kwargs)
    return decorated_function

@app.route('/api/predict', methods=['POST'])
@require_auth
def api_predict():
    # Protected endpoint
    pass
```

### Network Security

```yaml
# network-policy.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: cctv-detection-netpol
  namespace: cctv-detection
spec:
  podSelector:
    matchLabels:
      app: cctv-threat-detection
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 7860
  egress:
  - to: []
    ports:
    - protocol: TCP
      port: 443  # HTTPS
    - protocol: TCP
      port: 80   # HTTP
```

## Backup & Recovery

### Database Backup

```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"

# Backup models
tar -czf $BACKUP_DIR/models_$DATE.tar.gz ./models/

# Backup configurations
tar -czf $BACKUP_DIR/configs_$DATE.tar.gz ./configs/

# Backup logs (last 7 days)
find ./logs -name "*.log" -mtime -7 | tar -czf $BACKUP_DIR/logs_$DATE.tar.gz -T -

# Clean old backups (keep 30 days)
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

### Disaster Recovery

```bash
#!/bin/bash
# restore.sh
BACKUP_FILE=$1

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup_file>"
    exit 1
fi

# Stop services
docker-compose down

# Restore from backup
tar -xzf $BACKUP_FILE -C ./

# Restart services
docker-compose up -d

echo "Restore completed"
```

## Performance Optimization

### Model Optimization

```python
# Optimize model for inference
import torch
from torch.jit import script

# Load model
model = CCTVThreatDetector.from_pretrained("your-username/cctv-threat-detection")

# Convert to TorchScript
scripted_model = script(model.model)
scripted_model.save("optimized_model.pt")

# Use optimized model
optimized_model = torch.jit.load("optimized_model.pt")
```

### Caching Strategy

```python
from functools import lru_cache
import hashlib

class CachedThreatDetector:
    def __init__(self, model_path):
        self.detector = CCTVThreatDetector.from_pretrained(model_path)
    
    @lru_cache(maxsize=1000)
    def predict_cached(self, image_hash):
        # Cache predictions based on image hash
        return self.detector.predict_image(image_path)
    
    def predict_image(self, image_path):
        # Calculate image hash
        with open(image_path, 'rb') as f:
            image_hash = hashlib.md5(f.read()).hexdigest()
        
        return self.predict_cached(image_hash)
```

## Troubleshooting

### Common Deployment Issues

1. **Out of Memory**
   ```bash
   # Increase memory limits
   docker run --memory=4g cctv-threat-detection
   ```

2. **GPU Not Available**
   ```bash
   # Check GPU availability
   nvidia-smi
   docker run --gpus all nvidia/cuda:11.8-runtime-ubuntu20.04 nvidia-smi
   ```

3. **Port Conflicts**
   ```bash
   # Check port usage
   netstat -tulpn | grep :7860
   # Use different port
   docker run -p 8080:7860 cctv-threat-detection
   ```

4. **Model Loading Issues**
   ```bash
   # Check model files
   ls -la ./models/
   # Download model manually
   huggingface-cli download your-username/cctv-threat-detection
   ```

## Maintenance

### Regular Tasks

```bash
#!/bin/bash
# maintenance.sh

# Update system packages
sudo apt-get update && sudo apt-get upgrade -y

# Clean Docker images
docker system prune -f

# Rotate logs
logrotate /etc/logrotate.d/cctv-detection

# Check disk space
df -h

# Monitor service health
curl -f http://localhost:7860/health || echo "Service unhealthy"
```

### Health Checks

```python
# Add to app.py
@app.route('/health')
def health_check():
    try:
        # Check model loading
        detector.predict_image(test_image)
        return jsonify({'status': 'healthy', 'timestamp': datetime.now().isoformat()})
    except Exception as e:
        return jsonify({'status': 'unhealthy', 'error': str(e)}), 500
```

This deployment guide provides comprehensive coverage for deploying the CCTV Threat Detection system in various environments, from local development to production-scale cloud deployments.
