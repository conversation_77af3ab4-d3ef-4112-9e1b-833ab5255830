import torch
from transformers import ViTImageProcessor, ViTForImageClassification
from PIL import Image
import cv2
import numpy as np
from typing import Dict, List, Union
import logging

class CCTVThreatDetector:
    """Main threat detection model class"""
    
    def __init__(self, model_name_or_path: str = None, confidence_threshold: float = 0.7):
        self.confidence_threshold = confidence_threshold
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Load model and processor
        if model_name_or_path:
            self.load_model(model_name_or_path)
        else:
            self.processor = None
            self.model = None
            
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    @classmethod
    def from_pretrained(cls, model_name_or_path: str, **kwargs):
        """Load pretrained model from Hugging Face Hub or local path"""
        detector = cls(**kwargs)
        detector.load_model(model_name_or_path)
        return detector
    
    def load_model(self, model_name_or_path: str):
        """Load model and processor"""
        try:
            self.processor = ViTImageProcessor.from_pretrained(model_name_or_path)
            self.model = ViTForImageClassification.from_pretrained(model_name_or_path)
            self.model.to(self.device)
            self.model.eval()
            
            self.logger.info(f"Model loaded successfully from {model_name_or_path}")
            self.logger.info(f"Using device: {self.device}")
            
        except Exception as e:
            self.logger.error(f"Failed to load model: {e}")
            raise
    
    def preprocess_image(self, image: Union[str, Image.Image, np.ndarray]) -> torch.Tensor:
        """Preprocess image for model input"""
        if isinstance(image, str):
            image = Image.open(image).convert('RGB')
        elif isinstance(image, np.ndarray):
            image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        elif not isinstance(image, Image.Image):
            raise ValueError("Unsupported image type")
        
        inputs = self.processor(image, return_tensors="pt")
        return {k: v.to(self.device) for k, v in inputs.items()}
    
    def predict_image(self, image: Union[str, Image.Image, np.ndarray]) -> Dict:
        """Predict threat in single image"""
        if self.model is None:
            raise ValueError("Model not loaded. Use load_model() or from_pretrained()")
        
        inputs = self.preprocess_image(image)
        
        with torch.no_grad():
            outputs = self.model(**inputs)
            probabilities = torch.nn.functional.softmax(outputs.logits, dim=-1)
            predicted_class = torch.argmax(probabilities, dim=-1).item()
            confidence = probabilities[0][predicted_class].item()
        
        return {
            'is_threat': predicted_class == 1,
            'confidence': confidence,
            'probabilities': probabilities[0].cpu().numpy(),
            'raw_logits': outputs.logits[0].cpu().numpy()
        }
    
    def predict_video(self, video_path: str, frame_skip: int = 30) -> List[Dict]:
        """Predict threats in video file"""
        cap = cv2.VideoCapture(video_path)
        results = []
        frame_count = 0
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                if frame_count % frame_skip == 0:
                    result = self.predict_image(frame)
                    result['frame_number'] = frame_count
                    result['timestamp'] = frame_count / cap.get(cv2.CAP_PROP_FPS)
                    results.append(result)
                
                frame_count += 1
                
        finally:
            cap.release()
        
        return results
    
    def predict_batch(self, images: List[Union[str, Image.Image, np.ndarray]]) -> List[Dict]:
        """Predict threats in batch of images"""
        results = []
        for image in images:
            try:
                result = self.predict_image(image)
                results.append(result)
            except Exception as e:
                self.logger.error(f"Error processing image: {e}")
                results.append({
                    'is_threat': False,
                    'confidence': 0.0,
                    'error': str(e)
                })
        
        return results
