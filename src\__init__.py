"""
CCTV Threat Detection Package

A comprehensive package for real-time threat detection in CCTV footage using Vision Transformers.
"""

__version__ = "1.0.0"
__author__ = "Your Name"
__email__ = "<EMAIL>"

from .models.threat_detector import CCTVThreatDetector
from .inference.realtime import RealtimeProcessor
from .inference.ip_camera import IPCameraProcessor

__all__ = [
    "CCTVThreatDetector",
    "RealtimeProcessor", 
    "IPCameraProcessor"
]
