# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyTorch
*.pth
*.pt
*.onnx

# Jupyter Notebook
.ipynb_checkpoints

# Environment
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
logs/
checkpoints/
outputs/
threat_detections/
*.log

# Data
data/
datasets/
*.zip
*.tar.gz

# Model files (large files)
models/*.bin
models/*.safetensors

# Uploads and results
uploads/
results/
recordings/
backups/

# Cache
cache/
.cache/

# Temporary files
tmp/
temp/
*.tmp

# Configuration files with sensitive data
configs/*_local.yaml
configs/*_secret.json
.env.local
.env.production

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/

# Documentation builds
docs/_build/
site/

# Gradio temporary files
gradio_cached_examples/
flagged/
