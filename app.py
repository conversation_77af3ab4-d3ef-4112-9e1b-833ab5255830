import gradio as gr
import torch
from PIL import Image
import numpy as np
from src.models.threat_detector import CCTVThreatDetector

# Load model - using a more appropriate model or fallback
try:
    # Try to load a proper threat detection model first
    detector = CCTVThreatDetector.from_pretrained("kroeungcyber/cctv-threat-detection")
    print("✅ Loaded specialized threat detection model")
except Exception as e:
    print(f"⚠️ Could not load specialized model: {e}")
    try:
        # Fallback to base ViT with custom threat detection logic
        detector = CCTVThreatDetector.from_pretrained("google/vit-base-patch16-224-in21k", confidence_threshold=0.6)
        print("⚠️ Using base ViT model with custom threat detection logic")
    except Exception as e2:
        print(f"⚠️ Could not load any model: {e2}")
        # Create empty detector for demo
        detector = CCTVThreatDetector(confidence_threshold=0.6)
        print("⚠️ Using fallback detection method")

# Add temporal smoothing for live camera
class TemporalSmoother:
    def __init__(self, window_size=5, threshold=0.6):
        self.window_size = window_size
        self.threshold = threshold
        self.recent_predictions = []
        self.recent_confidences = []

    def add_prediction(self, is_threat, confidence):
        self.recent_predictions.append(is_threat)
        self.recent_confidences.append(confidence)

        # Keep only recent predictions
        if len(self.recent_predictions) > self.window_size:
            self.recent_predictions.pop(0)
            self.recent_confidences.pop(0)

    def get_smoothed_prediction(self):
        if not self.recent_predictions:
            return False, 0.0

        # Calculate smoothed values
        threat_ratio = sum(self.recent_predictions) / len(self.recent_predictions)
        avg_confidence = sum(self.recent_confidences) / len(self.recent_confidences)

        # Apply threshold with hysteresis
        smoothed_threat = threat_ratio >= self.threshold

        return smoothed_threat, avg_confidence

# Initialize temporal smoother
smoother = TemporalSmoother(window_size=7, threshold=0.4)

def predict_threat(image):
    """Predict threat from uploaded image"""
    if image is None:
        return "Please upload an image", 0.0, None

    try:
        result = detector.predict_image(image)

        # Enhanced prediction with more details
        is_threat = result['is_threat']
        confidence = result['confidence']

        if is_threat:
            prediction = f"🚨 THREAT DETECTED\nConfidence: {confidence:.2%}"
            if confidence < 0.7:
                prediction += "\n⚠️ Low confidence - verify manually"
        else:
            prediction = f"✅ Normal Activity\nConfidence: {confidence:.2%}"
            if confidence < 0.5:
                prediction += "\n⚠️ Uncertain - monitor closely"

        # Create confidence visualization
        import matplotlib.pyplot as plt
        fig, ax = plt.subplots(figsize=(8, 4))

        classes = ['Normal', 'Threat']
        probabilities = result['probabilities']
        colors = ['green', 'red']

        bars = ax.bar(classes, probabilities, color=colors, alpha=0.7)
        ax.set_title(f"Prediction Confidence")
        ax.set_ylabel('Probability')
        ax.set_ylim(0, 1)

        # Add threshold line
        ax.axhline(y=detector.confidence_threshold, color='orange', linestyle='--',
                  label=f'Threshold ({detector.confidence_threshold:.1%})')
        ax.legend()

        for bar, prob in zip(bars, probabilities):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                   f'{prob:.3f}', ha='center', va='bottom')

        plt.tight_layout()

        return prediction, confidence, fig

    except Exception as e:
        return f"Error: {str(e)}", 0.0, None

def predict_video(video_path):
    """Predict threats in video file"""
    if video_path is None:
        return "Please upload a video", []
    
    try:
        results = detector.predict_video(video_path)
        
        # Format results for display
        threat_frames = []
        for i, result in enumerate(results):
            if result['is_threat']:
                threat_frames.append({
                    'frame': i,
                    'timestamp': f"{i/30:.1f}s",  # Assuming 30 FPS
                    'confidence': result['confidence']
                })
        
        if threat_frames:
            summary = f"⚠️ {len(threat_frames)} threats detected in video"
            details = "\n".join([
                f"Frame {t['frame']} ({t['timestamp']}): {t['confidence']:.2%} confidence"
                for t in threat_frames[:10]  # Show first 10
            ])
            return f"{summary}\n\n{details}"
        else:
            return "✅ No threats detected in video"
            
    except Exception as e:
        return f"Error processing video: {str(e)}"

# Create Gradio interface
with gr.Blocks(
    title="Real Time Threat Identification",
    theme=gr.themes.Soft(),
    css="""
    footer {visibility: hidden}
    .gradio-container {margin-top: 0px !important}
    """
) as demo:
    gr.Markdown("""
    # 🔍 Real Time Threat Identification

    Upload images or videos to detect potential security threats using AI.
    The model can identify violent behavior, weapons, and suspicious activities.
    """)
    
    with gr.Tab("Image Analysis"):
        with gr.Row():
            with gr.Column():
                image_input = gr.Image(
                    type="pil", 
                    label="Upload CCTV Image",
                    height=300
                )
                image_button = gr.Button("🔍 Analyze Image", variant="primary")
                
            with gr.Column():
                image_prediction = gr.Textbox(
                    label="Prediction Result",
                    lines=2
                )
                image_confidence = gr.Number(
                    label="Confidence Score",
                    precision=3
                )
                confidence_plot = gr.Plot(label="Confidence Breakdown")
        
        image_button.click(
            predict_threat,
            inputs=[image_input],
            outputs=[image_prediction, image_confidence, confidence_plot]
        )
    
    with gr.Tab("Video Analysis"):
        with gr.Row():
            with gr.Column():
                video_input = gr.Video(
                    label="Upload CCTV Video",
                    height=300
                )
                video_button = gr.Button("🎥 Analyze Video", variant="primary")
                
            with gr.Column():
                video_results = gr.Textbox(
                    label="Analysis Results",
                    lines=10,
                    max_lines=20
                )
        
        video_button.click(
            predict_video,
            inputs=[video_input],
            outputs=[video_results]
        )
    
    with gr.Tab("Live Camera"):
        with gr.Row():
            with gr.Column():
                webcam_input = gr.Image(label="Laptop Camera", sources=["webcam"], streaming=True)

                with gr.Row():
                    start_button = gr.Button("🚀 Start Detection", variant="primary")
                    stop_button = gr.Button("⏹️ Stop Detection", variant="secondary")

                detection_status = gr.Textbox(label="Status", value="Ready", interactive=False)
                threat_counter = gr.Number(label="Threats Detected", value=0, precision=0, interactive=False)
            
            with gr.Column():
                webcam_prediction = gr.Textbox(label="Prediction Result", lines=2)
                webcam_confidence = gr.Number(label="Confidence Score", precision=5)
                webcam_plot = gr.Plot(label="Confidence Breakdown")
        
        # Store detection state
        detection_active = gr.State(False)
        threat_count = gr.State(0)
        
        def process_webcam(image, is_active, current_count):
            if image is None:
                return "No image captured", 0.0, None, is_active, current_count

            if not is_active:
                smoother.recent_predictions.clear()  # Reset smoother when inactive
                smoother.recent_confidences.clear()
                return "Detection not active", 0.0, None, is_active, current_count

            # Get raw prediction
            try:
                result = detector.predict_image(image)
                raw_threat = result['is_threat']
                raw_confidence = result['confidence']

                # Add to temporal smoother
                smoother.add_prediction(raw_threat, raw_confidence)

                # Get smoothed prediction
                smoothed_threat, smoothed_confidence = smoother.get_smoothed_prediction()

                # Create prediction text with both raw and smoothed info
                if smoothed_threat:
                    prediction = f"🚨 THREAT DETECTED (Smoothed: {smoothed_confidence:.2%})"
                    current_count += 1
                else:
                    prediction = f"✅ Normal Activity (Smoothed: {smoothed_confidence:.2%})"

                # Add raw prediction info
                prediction += f"\nRaw: {'Threat' if raw_threat else 'Normal'} ({raw_confidence:.2%})"

                # Create confidence visualization
                import matplotlib.pyplot as plt
                fig, ax = plt.subplots(figsize=(8, 4))

                classes = ['Normal', 'Threat']
                probabilities = [1-smoothed_confidence, smoothed_confidence]
                colors = ['green', 'red']

                bars = ax.bar(classes, probabilities, color=colors, alpha=0.7)
                ax.set_title(f"Smoothed Prediction Confidence")
                ax.set_ylabel('Probability')
                ax.set_ylim(0, 1)

                for bar, prob in zip(bars, probabilities):
                    ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                           f'{prob:.3f}', ha='center', va='bottom')

                plt.tight_layout()

                return prediction, smoothed_confidence, fig, is_active, current_count

            except Exception as e:
                return f"Error: {str(e)}", 0.0, None, is_active, current_count
        
        def start_detection():
            return True, "Detection Active"
        
        def stop_detection():
            return False, "Detection Stopped"
        
        def update_ui(is_active, status_text, count):
            return status_text, count
        
        # Start/Stop detection
        start_button.click(
            fn=start_detection,
            outputs=[detection_active, detection_status]
        ).then(
            fn=update_ui,
            inputs=[detection_active, detection_status, threat_count],
            outputs=[detection_status, threat_counter]
        )
        
        stop_button.click(
            fn=stop_detection,
            outputs=[detection_active, detection_status]
        ).then(
            fn=update_ui,
            inputs=[detection_active, detection_status, threat_count],
            outputs=[detection_status, threat_counter]
        )
        
        # Real-time detection loop - process frames continuously when streaming
        webcam_input.stream(
            fn=process_webcam,
            inputs=[webcam_input, detection_active, threat_count],
            outputs=[webcam_prediction, webcam_confidence, webcam_plot, detection_active, threat_count],
            show_progress=False
        ).then(
            fn=update_ui,
            inputs=[detection_active, detection_status, threat_count],
            outputs=[detection_status, threat_counter]
        )
        

    


def main():
    """Main function to launch the demo"""
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_api=False
    )

if __name__ == "__main__":
    main()